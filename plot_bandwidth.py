import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import re
import platform

# 配置中文字体
def setup_chinese_font():
    """配置matplotlib支持中文显示"""
    system = platform.system()

    if system == "Darwin":  # macOS
        chinese_fonts = [
            'PingFang SC',      # 苹方
            'Hiragino Sans GB', # 冬青黑体
            'STHeiti',          # 华文黑体
            'SimHei'            # 黑体
        ]
    elif system == "Windows":  # Windows
        chinese_fonts = [
            'Microsoft YaHei',  # 微软雅黑
            'SimHei',          # 黑体
            'KaiTi',           # 楷体
            'SimSun'           # 宋体
        ]
    else:  # Linux
        chinese_fonts = [
            'WenQuanYi Micro Hei',
            'WenQuanYi Zen Hei',
            'Noto Sans CJK SC',
            'DejaVu Sans'
        ]

    # 尝试设置中文字体
    for font_name in chinese_fonts:
        try:
            font_list = [f.name for f in fm.fontManager.ttflist]
            if font_name in font_list:
                plt.rcParams['font.sans-serif'] = [font_name]
                plt.rcParams['axes.unicode_minus'] = False
                print(f"成功设置字体：{font_name}")
                return True
        except Exception as e:
            print(f"字体设置失败 {font_name}: {e}")
            continue

    # 如果没有找到合适的字体，使用默认设置
    print("警告：未找到合适的中文字体，使用默认字体")
    plt.rcParams['axes.unicode_minus'] = False
    return False

# 设置中文字体
setup_chinese_font()

def read_benchmark_data(filename):
    """从结果文件中读取基准测试数据"""
    sizes_kb = []
    gops = []

    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if not line:
                    continue

                # 解析数据格式: Size = 1024 KB, Time = 0.018 sec, Rate = 15.32 GOPS
                match = re.search(r'Size\s*=\s*(\d+)\s*KB.*Rate\s*=\s*([\d.]+)\s*GOPS', line)
                if match:
                    size = int(match.group(1))
                    rate = float(match.group(2))
                    sizes_kb.append(size)
                    gops.append(rate)

    except FileNotFoundError:
        print(f"错误：找不到文件 {filename}")
        print("请确保 result.txt 文件存在于当前目录中")
        return [], []
    except Exception as e:
        print(f"读取文件时发生错误：{e}")
        return [], []

    return sizes_kb, gops

def main():
    """主函数"""
    # 从 result.txt 读取数据
    sizes_kb, gops = read_benchmark_data('result.txt')

    # 检查数据有效性
    if not sizes_kb or not gops:
        print("错误：没有读取到有效数据，无法生成图表")
        return

    if len(sizes_kb) != len(gops):
        print("错误：数据长度不匹配")
        return

    print(f"成功读取 {len(sizes_kb)} 个数据点")
    print(f"大小范围：{min(sizes_kb)} KB - {max(sizes_kb)} KB")
    print(f"性能范围：{min(gops):.2f} - {max(gops):.2f} GOPS")

    # 创建图表
    plt.figure(figsize=(12, 8))
    plt.plot(sizes_kb, gops, marker='o', linewidth=2, markersize=6, 
             color='blue', markerfacecolor='red', alpha=0.8)
    
    # 设置对数坐标
    plt.xscale('log', base=2)
    
    # 设置标签和标题
    plt.xlabel("Array Size (KB, log2 scale)", fontsize=12)
    plt.ylabel("Increments per second (GOPS)", fontsize=12)
    plt.title("内存带宽基准测试 - Memory Bandwidth Benchmark", fontsize=14)
    
    # 添加网格
    plt.grid(True, alpha=0.3, linestyle='--')
    
    # 调整布局
    plt.tight_layout()

    try:
        # 保存图表
        plt.savefig("bandwidth_plot.png", dpi=300, bbox_inches='tight')
        print("图表已保存为 bandwidth_plot.png")
        
        # 显示图表
        plt.show()
    except Exception as e:
        print(f"保存或显示图表时出错：{e}")

if __name__ == "__main__":
    main()
